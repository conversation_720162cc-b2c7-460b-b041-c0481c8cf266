FROM node:20-slim
WORKDIR "/hospice-os"

# Copy package.json files for workspace resolution
COPY package.json package-lock.json tsconfig.json ./
COPY apps/server/package.json ./apps/server/
COPY packages/apptypes/package.json ./packages/apptypes/

# Install dependencies
RUN npm install -w @hospice-os/server

# Copy server source code
COPY apps/server/ ./apps/server/
# Copy any shared packages the server depends on
COPY packages/apptypes/ ./packages/apptypes/

# Remove any .env file unless running locally
ENV NODE_ENV=production
RUN rm -f ./apps/server/.env

# Build the server
RUN npm run build -w @hospice-os/server --force --ignore-ts-errors -- --skipLibCheck

# Clean up dev dependencies
RUN npm prune --omit=dev


# Set up the server as the working directory
WORKDIR "/hospice-os/apps/server"


# Use the entrypoint script
ENTRYPOINT ["node", "dist/app.js"]
