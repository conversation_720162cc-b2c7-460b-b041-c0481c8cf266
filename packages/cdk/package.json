{"name": "@hospice-os/cdk", "version": "0.1.0", "bin": {"cdk": "bin/app.js"}, "scripts": {"build": "tsc --skip<PERSON><PERSON><PERSON><PERSON><PERSON>", "watch": "tsc -w", "test": "vitest run", "bootstrap:staging": "cdk bootstrap --all -c stage=staging --profile hospice-os-staging --require-approval never", "bootstrap:prod": "cdk bootstrap --all -c stage=prod --profile hospice-os-prod --require-approval never", "deploy:staging": "cdk deploy --all -c stage=staging --profile hospice-os-staging --require-approval never", "deploy:prod": "cdk deploy --all -c stage=prod --profile hospice-os-prod --require-approval never", "deploy:db": "cdk deploy '*db*'", "deploy:web": "cdk deploy '*web*'", "deploy:admin": "cdk deploy '*admin*'", "deploy:infra:staging": "cdk deploy '*infra*' -c stage=staging --require-approval never --profile hospice-os-staging", "deploy:infra:prod": "cdk deploy '*infra*' -c stage=prod --require-approval never --profile hospice-os-prod", "deploy:server:staging": "cdk deploy '*server*' -c stage=staging --require-approval never --profile hospice-os-staging", "deploy:server:prod": "cdk deploy '*server*' -c stage=prod --require-approval never --profile hospice-os-prod", "deploy:storage:staging": "cdk deploy '*storage*' -c stage=staging --require-approval never --profile hospice-os-staging", "deploy:storage:prod": "cdk deploy '*storage*' -c stage=prod --require-approval never --profile hospice-os-prod", "diff": "cdk diff", "synth:staging": "cdk synth -c stage=staging --profile hospice-os-staging", "params:push:staging": "ts-node scripts/manage-parameters.ts --cdk-json ./cdk.json --stage staging", "params:push:prod": "ts-node scripts/manage-parameters.ts --cdk-json ./cdk.json --stage prod"}, "dependencies": {"@aws-sdk/client-ssm": "^3.540.0", "aws-cdk-lib": "2.176.0", "constructs": "^10.0.0", "data-uri-to-buffer": "^4.0.1", "dotenv": "^16.4.5", "fetch-blob": "^3.2.0", "formdata-polyfill": "^4.0.10", "source-map-support": "^0.5.21", "yargs": "^17.7.2"}, "devDependencies": {"@types/node": "^22.16.2", "@types/yargs": "^17.0.32", "aws-cdk": "2.176.0", "ts-node": "^10.9.2", "typescript": "^5.7.3"}}