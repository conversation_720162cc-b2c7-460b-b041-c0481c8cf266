#!/bin/bash
# Script to deploy web application without updating CloudFormation

set -e

# You can override these variables with environment variables or by editing this file
AWS_DEFAULT_PROFILE=hospice-os-staging
STAGE=staging
AWS_REGION=us-east-1
STYTCH_PROJECT_ENV=test
STYTCH_PUBLIC_TOKEN=public-token-test-6f8424f3-856c-4135-b486-1a7acf3b8228
API_BASE_URL=https://api-staging.tallio.com
VITE_REDIRECT_URL=https://app-staging.tallio.com
CLOUDFRONT_DISTRIBUTION_ID=E22YNJYIY6QWTH


# Build the web app
echo "Building web app..."
API_BASE_URL=$API_BASE_URL \
STYTCH_PROJECT_ENV=$STYTCH_PROJECT_ENV \
STYTCH_PUBLIC_TOKEN=$STYTCH_PUBLIC_TOKEN \
VITE_REDIRECT_URL=$VITE_REDIRECT_URL \
npm run build -w @hospice-os/web

# Check if build was successful
if [ ! -d "apps/web/dist" ]; then
  echo "Error: Build failed or dist directory not found!"
fi

# Deploy to S3
echo "Deploying to S3..."
aws s3 sync apps/web/dist/ s3://hospice-os-web-staging-690071479855/ --delete --profile $AWS_DEFAULT_PROFILE

# Create CloudFront invalidation
echo "Creating CloudFront invalidation..."
INVALIDATION_ID=$(aws cloudfront create-invalidation \
  --distribution-id $CLOUDFRONT_DISTRIBUTION_ID \
  --paths "/*" \
  --query 'Invalidation.Id' \
  --output text \
  --profile $AWS_DEFAULT_PROFILE)

echo "Deployment completed successfully."
echo "CloudFront invalidation ($INVALIDATION_ID) in progress."
echo "You can check the invalidation status with:"
echo "aws cloudfront get-invalidation --distribution-id $CLOUDFRONT_DISTRIBUTION_ID --id $INVALIDATION_ID"
echo "Admin app will be available at: https://${CLOUDFRONT_DISTRIBUTION_ID}.cloudfront.net/"
