{"name": "@hospice-os/server", "version": "1.0.0", "description": "", "private": true, "main": "index.js", "scripts": {"build:server": "tsc --skip<PERSON><PERSON><PERSON><PERSON><PERSON>", "build": "tsc --skip<PERSON><PERSON><PERSON><PERSON><PERSON>", "build:workflow": "npm run build:workflowBundle && npm run build && cp -r src/temporal/certificates dist/temporal/certificates && cp -r src/temporal/workflow-bundle.js dist/temporal/workflow-bundle.js", "build:workflowBundle": "ts-node ./src/temporal/scripts/build-workflow-bundle.ts", "prebundle": "rm -rf dist", "bundle": "esbuild src/app.ts --bundle --minify --sourcemap --platform=node --target=es2020 --outfile=dist/index.js", "postbundle": "cd dist && zip -r index.zip index.js*", "app": "node ./dist/app.js", "app-dev": "ts-node-dev --watch ./src --respawn --transpile-only ./src/app.ts", "worker-dev": "ts-node-dev --watch ./src --respawn --transpile-only ./src/temporal/worker.ts", "app-dev:memory": "USE_IN_MEMORY_REPOSITORIES=true ts-node-dev --watch ./src --respawn --transpile-only ./src/app.ts", "lint": "eslint . --ext .ts", "test": "vitest run", "test:watch": "vitest", "test:ui": "vitest --ui", "db:migrate": "npx ts-node ./src/db/runMigrations.ts", "test:api": "ts-node src/test-api.ts"}, "author": "", "license": "ISC", "devDependencies": {"@eslint/js": "^9.18.0", "@restatedev/restate-sdk-testcontainers": "^1.6.0", "@types/cors": "^2.8.17", "@types/express": "^4.17.1", "@types/json-pointer": "^1.0.34", "@types/lodash": "^4.17.19", "constructs": "^10.0.0", "esbuild": "^0.24.2", "eslint": "^9.18.0", "globals": "^15.14.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.7.3", "typescript-eslint": "^8.21.0", "vitest": "^3.1.2"}, "dependencies": {"@aws-sdk/client-s3": "^3.796.0", "@aws-sdk/client-ssm": "^3.798.0", "@aws-sdk/s3-request-presigner": "^3.796.0", "@hospice-os/apptypes": "*", "@restatedev/restate-sdk": "^1.4.0", "@restatedev/restate-sdk-clients": "^1.4.0", "@temporalio/activity": "^1.11.8", "@temporalio/client": "^1.11.8", "@temporalio/common": "^1.11.8", "@temporalio/worker": "^1.11.8", "@temporalio/workflow": "^1.11.8", "@types/express": "^4.17.1", "@types/pg": "^8.11.13", "@types/sharp": "^0.32.0", "@types/uuid": "^10.0.0", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "assemblyai": "^4.12.2", "axios": "^1.8.4", "coda-js": "^4.2.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "express": "^4.17.1", "fast-json-patch": "^3.1.1", "json-pointer": "^0.6.2", "knex": "^3.1.0", "lodash": "^4.17.21", "openai": "^4.97.0", "page-count": "0.0.4", "pdf-img-convert": "^2.0.0", "pdf-parse-debugging-disabled": "^1.1.1", "pdf2pic": "^3.2.0", "pg": "^8.14.1", "pg-pool": "^3.8.0", "sharp": "^0.34.1", "stytch": "^10.0.0", "tesseract.js": "^6.0.1", "uuid": "^11.1.0", "uuidv7": "^1.0.2"}}